package com.inossem.wms.bizdomain.bi.dao;

import com.inossem.wms.common.model.bizdomain.bi.po.BiSearchPO;
import com.inossem.wms.common.model.bizdomain.bi.vo.BiCompletedContractAmountVO;
import com.inossem.wms.common.model.bizdomain.bi.vo.BiCompletedContractCountVO;
import com.inossem.wms.common.model.bizdomain.bi.vo.BiContractAmountDetailVO;
import com.inossem.wms.common.model.bizdomain.bi.entity.BiPurchaseBase;
import com.inossem.wms.common.model.bizdomain.bi.vo.BiPurchaseContractVO;
import com.inossem.wms.common.model.bizdomain.bi.vo.BiPurchaseDemandPlanVO;
import com.inossem.wms.common.model.bizdomain.bi.vo.BiPurchasePurchaseVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 大屏 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Mapper
public interface BiPurchaseMapper {

    /**
     * BI-查询已完成采购任务合同金额
     */
    BigDecimal selectContractAmountSum(BiSearchPO po);

    /**
     * BI-查询合同金额明细（包含币种和创建时间）
     */
    List<BiContractAmountDetailVO> selectContractDetailForAmountSum(@Param("year") Integer year);

    /**
     * BI-已完成采购任务的合同数量
     */
    List<BiCompletedContractCountVO> getCompletedContractCount(@Param("po") BiSearchPO po);

    /**
     * BI-已完成采购任务的合同金额
     */
    List<BiCompletedContractAmountVO> getCompletedContractAmount402(@Param("po") BiSearchPO po);

    /**
     * BI-已完成采购任务的合同金额
     */
    List<BiCompletedContractAmountVO> getCompletedContractAmount403(@Param("po") BiSearchPO po);

    /**
     * BI-查询所有需求计划单
     */
    List<BiPurchaseDemandPlanVO> selectDemandPlanHead();

    /**
     * BI-查询所有采购流程
     */
    List<BiPurchasePurchaseVO> selectPurchaseHead();

    /**
     * BI-查询所有合同单据
     */
    List<BiPurchaseContractVO> selectContractHead();

}

package com.inossem.wms.bizdomain.budget.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.bizdomain.bi.po.BiBudgetSearchPO;
import com.inossem.wms.common.model.bizdomain.bi.po.BiSearchPO;
import com.inossem.wms.common.model.bizdomain.bi.vo.BiBudgetClassifySubjectVO;
import com.inossem.wms.common.model.masterdata.budget.entity.DicAnnualBudget;
import com.inossem.wms.common.model.masterdata.budget.po.DicAnnualBudgetSearchPO;
import com.inossem.wms.common.model.masterdata.budget.vo.DicAnnualBudgetPageVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AnnualBudgetMapper extends WmsBaseMapper<DicAnnualBudget> {

    List<DicAnnualBudgetPageVO> selectPageVOList(IPage<DicAnnualBudgetPageVO> page, @Param("po") DicAnnualBudgetSearchPO po);

    /**
     * 查询预算分类列表
     *
     * @param po 查询条件 年份
     * @return 预算分类列表
     */
    List<BiBudgetClassifySubjectVO> selectBudgetClassifyList(@Param("po") BiSearchPO po);

    /**
     * 查询预算科目列表
     *
     * @param po 查询条件 年份
     * @return 预算科目列表
     */
    List<BiBudgetClassifySubjectVO> selectBudgetSubjectList(@Param("po") BiBudgetSearchPO po);
}

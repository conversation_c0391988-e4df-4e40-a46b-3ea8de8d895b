package com.inossem.wms.bizdomain.budget.service.datawrap;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizdomain.budget.dao.AnnualBudgetMapper;
import com.inossem.wms.common.model.bizdomain.bi.po.BiBudgetSearchPO;
import com.inossem.wms.common.model.bizdomain.bi.po.BiSearchPO;
import com.inossem.wms.common.model.bizdomain.bi.vo.BiBudgetClassifySubjectVO;
import com.inossem.wms.common.model.masterdata.budget.entity.DicAnnualBudget;
import com.inossem.wms.common.model.masterdata.budget.po.DicAnnualBudgetSearchPO;
import com.inossem.wms.common.model.masterdata.budget.vo.DicAnnualBudgetPageVO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/15
 */
@Service
public class AnnualBudgetDataWrap extends BaseDataWrap<AnnualBudgetMapper, DicAnnualBudget> {

    public List<DicAnnualBudgetPageVO> selectPageVOList(IPage<DicAnnualBudgetPageVO> page, DicAnnualBudgetSearchPO po) {
        return this.baseMapper.selectPageVOList(page, po);
    }

    /**
     * 查询预算分类列表
     *
     * @param po 查询条件 年份
     * @return 预算分类列表
     */
    public List<BiBudgetClassifySubjectVO> selectBudgetClassifyList(BiSearchPO po) {
        return this.baseMapper.selectBudgetClassifyList(po);
    }

    /**
     * 查询预算科目列表
     *
     * @param po 查询条件 年份
     * @return 预算科目列表
     */
    public List<BiBudgetClassifySubjectVO> selectBudgetSubjectList(BiBudgetSearchPO po) {
        return this.baseMapper.selectBudgetSubjectList(po);
    }

}

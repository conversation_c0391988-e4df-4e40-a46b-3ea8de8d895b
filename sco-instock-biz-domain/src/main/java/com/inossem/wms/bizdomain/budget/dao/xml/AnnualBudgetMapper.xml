<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.budget.dao.AnnualBudgetMapper">

    <select id="selectPageVOList" resultType="com.inossem.wms.common.model.masterdata.budget.vo.DicAnnualBudgetPageVO">
        SELECT
        dab.*,
        su.user_code as createUserCode,
        su.user_name as createUserName,
        dbc.budget_classify_code as budgetClassifyCode,
        dbc.budget_classify_name as budgetClassifyName,
        dbs.budget_subject_code as budgetSubjectCode,
        dbs.budget_subject_name as budgetSubjectName,
        concat(dab.year,'年-',dbc.budget_classify_name,'-',dbs.budget_subject_name) as uniqueKey
        FROM
        dic_annual_budget dab
        inner join sys_user su ON dab.create_user_id = su.id
        inner join dic_budget_classify dbc ON dbc.id = dab.budget_classify_id
        inner join dic_budget_subject dbs ON dbs.id = dab.budget_subject_id
        AND su.is_delete = 0
        AND dbc.is_delete = 0
        AND dab.is_delete = 0
        <where>
            <if test="po.budgetClassifyName != null and po.budgetClassifyName != ''">
                AND dbc.budget_classify_name like concat('%', #{po.budgetClassifyName}, '%')
            </if>
            <if test="po.budgetSubjectName != null and po.budgetSubjectName != ''">
                AND dbs.budget_subject_name like concat('%', #{po.budgetSubjectName}, '%')
            </if>
        </where>
        order by dab.id asc
    </select>

    <select id="selectBudgetClassifyList" resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiBudgetClassifySubjectVO">
        SELECT dbc.id,
               dbc.budget_classify_code          as budget_code,
               dbc.budget_classify_name          as budget_name,
               sum(dab.budget_amount)            as budget_amount,
               sum(dab.existing_contract_amount) as contract_amount,
               su.user_code                      as create_user_code,
               su.user_name                      as create_user_name,
               dbc.create_time
        FROM dic_annual_budget dab
                 inner join dic_budget_classify dbc ON dbc.id = dab.budget_classify_id AND dbc.is_delete = 0
                 left join sys_user su ON dbc.create_user_id = su.id AND su.is_delete = 0
        where dab.is_delete = 0
          and dab.year = #{po.year}
        group by dbc.id
    </select>

    <select id="selectBudgetSubjectList" resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiBudgetClassifySubjectVO">
        SELECT dbs.id,
               dbs.budget_subject_code           as budget_code,
               dbs.budget_subject_name           as budget_name,
               sum(dab.budget_amount)            as budget_amount,
               sum(dab.existing_contract_amount) as contract_amount,
               su.user_code                      as create_user_code,
               su.user_name                      as create_user_name,
               dbs.create_time
        FROM dic_annual_budget dab
                 inner join dic_budget_classify dbc ON dbc.id = dab.budget_classify_id AND dbc.is_delete = 0
                 inner join dic_budget_subject dbs ON dbs.id = dab.budget_subject_id AND dbs.is_delete = 0
                 left join sys_user su ON dbs.create_user_id = su.id AND su.is_delete = 0
        where dab.is_delete = 0
          and dab.year = #{po.year}
          and dab.budget_classify_id = #{po.budgetClassifyId}
        group by dbs.id
    </select>

</mapper>
